import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, Clock, DollarSign, Calendar, Target, Award, Timer, Globe, Crown, TrendingUp, TrendingDown, Search, Filter, SortDesc, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import CompetitionCountdown from './CompetitionCountdown';
import CompetitionCreatorHub from './CompetitionCreatorHub';
import CompetitionCreatorDashboard from './CompetitionCreatorDashboard';
import { OFFICIAL_OSIS_BUSINESS_ID, OFFICIAL_OSIS_HANDLE } from '@/types/whopCompetition';
import { useAuth } from '@/contexts/AuthContext';

interface CompetitionDiscoveryProps {
  onCompetitionSelect?: (competition: Competition) => void;
}

// Clean Leaderboard Component for Discovery
const CleanLeaderboard: React.FC<{ competitionId: string }> = ({ competitionId }) => {
  const { getCompetitionDetails } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        const details = await getCompetitionDetails(competitionId);
        if (details) {
          setLeaderboard(details.leaderboard || []);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
    const interval = setInterval(fetchLeaderboard, 30000);
    return () => clearInterval(interval);
  }, [competitionId, getCompetitionDetails]);

  const formatName = (username: string) => {
    const parts = username.split(' ');
    if (parts.length >= 2) {
      return `${parts[0]} ${parts[1].charAt(0)}.`;
    }
    return username;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  const getRankGlow = (rank: number) => {
    switch (rank) {
      case 1: return 'shadow-lg shadow-yellow-400/20 bg-gradient-to-r from-yellow-400/5 to-transparent';
      case 2: return 'shadow-lg shadow-gray-300/15 bg-gradient-to-r from-gray-300/5 to-transparent';
      case 3: return 'shadow-lg shadow-amber-600/15 bg-gradient-to-r from-amber-600/5 to-transparent';
      default: return '';
    }
  };

  const getRankNumberStyle = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-black shadow-lg shadow-yellow-400/30';
      case 2: return 'bg-gradient-to-br from-gray-300 to-gray-500 text-black shadow-lg shadow-gray-300/30';
      case 3: return 'bg-gradient-to-br from-amber-600 to-amber-800 text-white shadow-lg shadow-amber-600/30';
      default: return 'bg-[#1a1a1a] border border-white/20 text-white/80';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <Card className="bg-[#141414] border-white/[0.08] shadow-2xl overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-[#141414] to-[#1a1a1a]">
        <CardTitle className="text-white flex items-center gap-3 text-2xl">
          <Trophy className="w-7 h-7 text-yellow-400 drop-shadow-lg" />
          <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
            Leaderboard
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-0">
          {leaderboard.slice(0, 10).map((entry, index) => (
            <motion.div
              key={entry.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05, type: "spring", stiffness: 100 }}
              className={`flex items-center justify-between p-6 hover:bg-white/[0.02] transition-all duration-300 border-b border-white/[0.05] last:border-b-0 ${getRankGlow(entry.current_rank)}`}
            >
              {/* Left side - Rank and User */}
              <div className="flex items-center gap-6">
                {/* Rank Number */}
                <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankNumberStyle(entry.current_rank)}`}>
                  {entry.current_rank}
                </div>

                {/* User Info */}
                <div>
                  <h4 className="text-white font-bold text-lg">
                    {formatName(entry.username)}
                  </h4>
                </div>
              </div>

              {/* Right side - Performance */}
              <div className="text-right">
                <div className="text-white font-bold text-xl mb-1">
                  {formatCurrency(entry.portfolio_value)}
                </div>
                <div className={`font-bold text-lg ${
                  entry.return_percent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatPercentage(entry.return_percent)}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const CompetitionDiscovery: React.FC<CompetitionDiscoveryProps> = ({
  onCompetitionSelect
}) => {
  const { competitions, userCompetitions, loading, joinCompetition, refreshCompetitions } = useCompetitions();
  const { user } = useAuth();
  const { toast } = useToast();
  const [filter, setFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');
  const [showCreatorHub, setShowCreatorHub] = useState(false);
  const [selectedCreatorCompetition, setSelectedCreatorCompetition] = useState<Competition | null>(null);
  const [viewMode, setViewMode] = useState<'discover' | 'my-competitions' | 'creator-dashboard'>('discover');

  // Fetch competitions on mount
  useEffect(() => {
    refreshCompetitions();
  }, [refreshCompetitions]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getCompetitionScopeBadge = (competition: Competition) => {
    if (!competition.competition_scope || competition.competition_scope === 'public') {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
          <Globe className="w-3 h-3 mr-1" />
          Public
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_local') {
      return (
        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
          <Users className="w-3 h-3 mr-1" />
          Community
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_cross_community') {
      const isOfficialOsis = competition.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                            competition.whop_business_handle === OFFICIAL_OSIS_HANDLE;
      return (
        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
          <Crown className="w-3 h-3 mr-1" />
          {isOfficialOsis ? 'Official Osis' : 'Cross-Community'}
        </Badge>
      );
    }

    return null;
  };

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        urgent: false
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      const remaining = formatDistanceToNow(end);
      return {
        label: 'Ends in',
        time: remaining,
        urgent: remaining.includes('hour') || remaining.includes('minute')
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        urgent: false
      };
    }
  };

  const isUserParticipating = (competitionId: string) => {
    return userCompetitions.some(uc => uc.competition_id === competitionId);
  };

  const handleJoinCompetition = async (competitionId: string) => {
    try {
      await joinCompetition(competitionId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  // Get user's created competitions
  const myCreatedCompetitions = competitions.filter(competition =>
    competition.creator_id === user?.id
  );

  // Get competitions to display based on view mode
  const getDisplayCompetitions = () => {
    if (viewMode === 'my-competitions') {
      return myCreatedCompetitions;
    }
    return competitions;
  };

  const filteredCompetitions = getDisplayCompetitions().filter(competition => {
    // Status filter
    if (filter !== 'all' && competition.status !== filter) return false;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        competition.name.toLowerCase().includes(query) ||
        competition.description?.toLowerCase().includes(query)
      );
    }

    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'participants':
        return (b.participant_count || 0) - (a.participant_count || 0);
      case 'prize':
        return (b.starting_balance || 0) - (a.starting_balance || 0);
      case 'newest':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  // Debug logging
  console.log('All competitions:', competitions);
  console.log('Filtered competitions:', filteredCompetitions);
  console.log('View mode:', viewMode);
  console.log('User ID:', user?.id);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  // Show creator dashboard if selected
  if (viewMode === 'creator-dashboard' && selectedCreatorCompetition) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] p-6">
        <div className="max-w-7xl mx-auto">
          <CompetitionCreatorDashboard
            competition={selectedCreatorCompetition}
            onBack={() => {
              setViewMode('my-competitions');
              setSelectedCreatorCompetition(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white overflow-hidden flex flex-col items-center">
      {/* Header with Logo and Analytics Button */}
      <div className="w-full flex justify-between items-center px-8 pt-6 pb-4">
        <img
          src="http://thecodingkid.oyosite.com/logo_only.png"
          alt="OSIS Logo"
          className="w-8 h-8"
        />

        {/* Analytics Quick Access */}
        {myCreatedCompetitions.length > 0 && (
          <button
            onClick={() => setViewMode('my-competitions')}
            className="bg-green-500/[0.1] hover:bg-green-500/[0.15] border border-green-500/[0.3] hover:border-green-500/[0.5] text-green-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(34,197,94,0.1)] hover:shadow-[inset_0_1px_0_rgba(34,197,94,0.2)] backdrop-blur-sm active:scale-[0.98]"
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
          >
            <Trophy className="w-4 h-4 mr-2 inline" />
            My Analytics
          </button>
        )}
      </div>

      {/* Title */}
      <div className="text-center mb-8 mt-8">
        <h1 className="text-4xl font-light text-white tracking-tight">
          {viewMode === 'my-competitions' ? 'My Competitions' : 'Discover Trading Competitions'}
        </h1>
        <p className="text-white/60 mt-2 text-lg">
          {viewMode === 'my-competitions'
            ? 'Manage and track your competitions'
            : 'Join competitions or create your own'
          }
        </p>
      </div>

      {/* View Mode Tabs */}
      <div className="flex justify-center mb-6">
        <div className="flex items-center gap-1 bg-white/[0.05] border border-white/[0.08] rounded-lg p-1">
          <button
            onClick={() => setViewMode('discover')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              viewMode === 'discover'
                ? 'bg-white/[0.1] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                : 'text-white/70 hover:text-white hover:bg-white/[0.05]'
            }`}
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
          >
            Discover
          </button>
          <button
            onClick={() => setViewMode('my-competitions')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              viewMode === 'my-competitions'
                ? 'bg-white/[0.1] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                : 'text-white/70 hover:text-white hover:bg-white/[0.05]'
            }`}
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
          >
            My Competitions ({myCreatedCompetitions.length})
          </button>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center items-center gap-4 mb-8">
        <button
          onClick={() => setShowCreatorHub(true)}
          className="bg-white/[0.05] hover:bg-white/[0.08] border border-white/[0.08] hover:border-white/[0.12] text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-sm active:scale-[0.98]"
          style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
        >
          <Plus className="w-3 h-3 mr-1.5 inline" />
          Create Competition
        </button>

        {/* Analytics Navigation Button */}
        {myCreatedCompetitions.length > 0 && (
          <button
            onClick={() => setViewMode('my-competitions')}
            className="bg-green-500/[0.1] hover:bg-green-500/[0.15] border border-green-500/[0.3] hover:border-green-500/[0.5] text-green-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(34,197,94,0.1)] hover:shadow-[inset_0_1px_0_rgba(34,197,94,0.2)] backdrop-blur-sm active:scale-[0.98]"
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
          >
            <Trophy className="w-3 h-3 mr-1.5 inline" />
            View My Analytics ({myCreatedCompetitions.length})
          </button>
        )}
      </div>

      {/* Search Bar */}
      <div className="w-full max-w-md mb-8">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//Tradingview%20Financial%20Chart.png"
              alt="Search"
              className="w-4 h-4 opacity-60"
            />
          </div>
          <input
            type="text"
            placeholder="Search for anything..."
            className="w-full pl-12 pr-4 py-3 bg-[#2A2A2A] border border-white/20 rounded-full text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 shadow-lg"
            style={{
              boxShadow: '0 0 20px rgba(255, 255, 255, 0.1), inset 0 1px 3px rgba(255, 255, 255, 0.1)'
            }}
          />
        </div>
      </div>

      {/* Filter Toggles */}
      <div className="flex items-center gap-6 mb-16">
        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Status:</span>
          <div className="flex gap-1">
            {['all', 'open', 'active', 'completed'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  filter === status
                    ? 'bg-white/20 text-white border border-white/30'
                    : 'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Sort:</span>
          <div className="flex gap-1">
            {[
              { key: 'newest', label: 'Newest' },
              { key: 'participants', label: 'Popular' },
              { key: 'prize', label: 'Prize' }
            ].map((option) => (
              <button
                key={option.key}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Competition Grid */}
      <div className="flex-1 w-full max-w-4xl px-8">
        <div className="grid grid-cols-2 gap-6 mb-8">
          {/* Always show 4 cards - fill with real competitions first, then placeholders */}
          {Array.from({ length: 4 }).map((_, index) => {
            const competition = filteredCompetitions[index];

            if (competition) {
              // Show real competition data
              return (
                <div
                  key={competition.id}
                  className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10 relative overflow-hidden hover:border-white/20 transition-all duration-200 cursor-pointer group"
                  onClick={() => {
                    if (viewMode === 'my-competitions' && competition.creator_id === user?.id) {
                      setSelectedCreatorCompetition(competition);
                      setViewMode('creator-dashboard');
                    } else {
                      onCompetitionSelect?.(competition);
                    }
                  }}
                >
                  <div className="absolute top-3 left-3 text-white/60 text-sm font-medium">
                    {competition.name}
                  </div>

                  <div className="absolute top-3 right-3">
                    <Badge className={getStatusColor(competition.status)}>
                      {competition.status}
                    </Badge>
                  </div>

                  <div className="absolute bottom-3 left-3 space-y-1">
                    <div className="text-white/40 text-xs">
                      {competition.participant_count || 0} participants
                    </div>
                    <div className="text-white/40 text-xs">
                      Prize: ${(competition.prize_pool || 0).toLocaleString()}
                    </div>
                  </div>

                  {competition.creator_id === user?.id && (
                    <div className="absolute bottom-3 right-3 flex items-center gap-2">
                      {viewMode === 'my-competitions' ? (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedCreatorCompetition(competition);
                            setViewMode('creator-dashboard');
                          }}
                          className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-1.5 rounded text-xs font-medium transition-all duration-200 border border-green-500/30 hover:border-green-500/50"
                          style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                        >
                          Manage
                        </button>
                      ) : (
                        <div className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs font-medium">
                          Creator
                        </div>
                      )}
                    </div>
                  )}

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-green-500/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </div>
              );
            } else {
              // Show placeholder card
              return (
                <div
                  key={`empty-${index}`}
                  className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10 relative overflow-hidden"
                >
                  {index === 0 && (
                    <>
                      <div className="absolute top-3 left-3 text-white/40 text-sm">
                        {viewMode === 'my-competitions' ? 'Create your first competition' : 'No competitions yet'}
                      </div>
                      <div className="absolute bottom-3 left-3 text-white/30 text-xs">
                        Click "Create Competition" above
                      </div>
                    </>
                  )}
                </div>
              );
            }
          })}
        </div>

        {filteredCompetitions.length > 4 && (
          <div className="text-center pb-8">
            <button className="text-white/60 text-sm hover:text-white/80 transition-colors">
              Show more competitions
            </button>
          </div>
        )}
      </div>

      {/* Competition Creator Hub Modal */}
      {showCreatorHub && (
        <CompetitionCreatorHub
          isOpen={showCreatorHub}
          onClose={() => setShowCreatorHub(false)}
          onCompetitionCreated={() => {
            setShowCreatorHub(false);
            refreshCompetitions();
            // Switch to "My Competitions" view to show the new competition
            setTimeout(() => {
              setViewMode('my-competitions');
            }, 500);
          }}
        />
      )}
    </div>
  );
};

export default CompetitionDiscovery;
